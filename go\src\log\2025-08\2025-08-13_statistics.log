{"level":"dev.info","ts":"[2025-08-13 09:10:42.336]","caller":"statistics/service.go:400","msg":"开始获取趋势统计数据","days":7}
{"level":"dev.error","ts":"[2025-08-13 09:10:42.360]","caller":"statistics/service.go:432","msg":"获取回款金额趋势数据失败","days":7,"error":"查询回款金额趋势数据失败: Error 1054 (42S22): Unknown column 'deleted_at' in 'where clause'","stacktrace":"fincore/app/business/statistics.(*Service).GetTrendStatistics.func2\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:432\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.info","ts":"[2025-08-13 09:11:35.273]","caller":"statistics/service.go:400","msg":"开始获取趋势统计数据","days":7}
{"level":"dev.info","ts":"[2025-08-13 09:11:35.421]","caller":"statistics/service.go:542","msg":"趋势统计数据获取完成","days":7,"data_points":7,"duration":"148.1234ms"}
