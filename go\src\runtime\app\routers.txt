/business/datacenter/attachment/get_picture
/business/datacenter/attachment/get_pictureCate
/business/datacenter/attachment/get_list
/business/datacenter/attachment/get_myFiles
/business/datacenter/common_config/get_config
/business/datacenter/configuration/get_email
/business/datacenter/dictionary/get_list
/business/datacenter/tabledata/get_list
/business/dashboard/workplace/get_message
/business/dashboard/workplace/get_msmContent
/business/dashboard/workplace/get_siteCount
/business/dashboard/workplace/get_statistical
/business/dashboard/workplace/get_popular
/business/dashboard/workplace/get_quick
/business/dashboard/workplace/get_visitlist
/business/developer/devapi/get_tablelist
/business/developer/devapi/get_tables
/business/developer/devapi/get_DBField
/business/developer/devapi/get_group
/business/developer/devapi/get_list
/business/developer/devapi/get_routes
/business/developer/devapi/getModel
/business/developer/devapigroup/get_list
/business/developer/devapigroup/get_parent
/business/developer/devapitype/get_list
/business/developer/devapitype/get_typeinfo
/business/developer/generatecode/get_dbfield
/business/developer/generatecode/get_list
/business/developer/generatecode/get_tablelist
/business/developer/generatecode/getContent
/business/system/role/get_list
/business/system/role/get_menuList
/business/system/role/get_parent
/business/system/rule/get_list
/business/system/rule/get_parent
/business/system/account/get_list
/business/system/account/get_loginloglist
/business/system/account/get_account
/business/system/account/get_role
/business/system/dept/get_list
/business/system/dept/get_parent
/business/system/member/get_list
/business/statistics/statisticscontroller/getChannelStatistics
/business/statistics/statisticscontroller/getHomeStatistics
/business/statistics/statisticscontroller/getTrendStatistics
/business/suggestions/complaint/getComplaintContentList
/business/customer/customercontroller/getCustomerDetail
/business/customer/customercontroller/getCustomerOptions
/business/customer/customercontroller/getCustomerRemarks
/business/customer/customercontroller/getCustomerStatistics
/business/customer/customercontroller/getRepurchaseAwakenRecords
/business/customer/customercontroller/getRepurchaseCustomerOptions
/business/channel/channelcontroller/getChannelDetail
/business/channel/channelcontroller/getChannelLoanRules
/business/channel/channelcontroller/getChannelOptions
/business/channel/channelcontroller/getProductRules
/business/common/message/get_list
/business/captcha/getCaptcha
/business/order/manager/getOrderPaymentRecords
/business/order/manager/getOrderProgress
/business/order/manager/getOrderBillInfo
/business/order/manager/getOrderCustomerInfo
/business/order/manager/getOrderStatus
/business/order/manager/getDisbursementStatus
/business/order/manager/getDownloadContract
/business/order/manager/getPendingOrders
/business/risk/riskcontroller/getProducts
/business/risk/riskcontroller/getProductsByAmount
/business/risk/riskcontroller/getEvaluate
/business/risk/riskcontroller/getForceRefreshRisk
/business/risk/riskcontroller/getReports
/business/repayment/manager/getPaymentStatus
/business/repayment/manager/getSubmittedAmounts
/business/user/get_code
/business/user/get_logininfo
/business/user/get_userinfo
/business/user/account/get_menu
/business/user/account/get_userdata
/business/user/data/get_user
/business/makecode/product/get_content
/business/makecode/product/get_list
/business/makecode/productcate/get_cate
/business/makecode/productcate/get_list
/business/makecode/cate/get_list
/business/bankcard/bankcardcontroller/getBankCardList
/business/bankcard/bankcardcontroller/getHealth
/business/productrules/list/getRules
/business/payment/manager/getMerchantsBalance
/uniapp/common/uploadfile/get_image
/uniapp/common/uploadfile/get_imagebase
/uniapp/common/fileaccess/getFileURL
/uniapp/captcha/getCaptcha
/uniapp/identity/getCheckFaceResult
/uniapp/identity/getFacePhoto
/uniapp/identity/getUserInfo
/uniapp/order/get_order_bills
/uniapp/order/get_repayment_preview
/uniapp/order/get_user_order_history
/uniapp/risk/riskcontroller/getEvaluate
/uniapp/risk/riskcontroller/getProducts
/uniapp/repayment/paymentcontroller/getPaymentStatus
/uniapp/bankcard/bankcardcontroller/getBankCardList
/uniapp/bankcard/bankcardcontroller/getHealth
/uniapp/user/complaint/getComplaintRes
/uniapp/user/getUserinfo
/uniapp/home/<USER>
/common/uploadfile/get_image
/common/uploadfile/get_imagebase
/common/install/index
/business/datacenter/attachment/del
/business/datacenter/dictionary/del
/business/datacenter/tabledata/del
/business/dashboard/workplace/del_quick
/business/developer/devapi/del
/business/developer/devapigroup/del
/business/developer/devapitype/del
/business/developer/generatecode/del
/business/system/role/del
/business/system/rule/del
/business/system/account/del
/business/system/dept/del
/business/makecode/product/del
/business/makecode/productcate/del
/business/makecode/cate/del
/business/channel/channelcontroller/deleteChannel
/business/productrules/list/delRule
/business/developer/devapi/save
/business/developer/devapi/upStatus
/business/developer/devapigroup/save
/business/developer/devapigroup/upStatus
/business/developer/devapitype/save
/business/developer/generatecode/upCodeTable
/business/developer/generatecode/upStatus
/business/developer/generatecode/uninstallcode
/business/developer/generatecode/save
/business/developer/apicode/installcode
/business/developer/apicode/removeFile
/business/developer/apicode/uninstallcode
/business/datacenter/attachment/save
/business/datacenter/attachment/upImgPid
/business/datacenter/common_config/saveConfig
/business/datacenter/configuration/saveEmail
/business/datacenter/dictionary/save
/business/datacenter/dictionary/upStatus
/business/datacenter/tabledata/save
/business/datacenter/upfile/uploadFile
/business/dashboard/workplace/saveQuick
/business/customer/customercontroller/updateCustomerQuota
/business/customer/customercontroller/updateCustomerRemark
/business/customer/customercontroller/updateCustomerStatus
/business/customer/customercontroller/unlockCustomer
/business/customer/customercontroller/listCustomers
/business/customer/customercontroller/listRepurchaseCustomers
/business/customer/customercontroller/exportCustomers
/business/customer/customercontroller/recordRepurchaseAwaken
/business/customer/customercontroller/sendRepurchaseSMS
/business/channel/channelcontroller/checkChannelStatus
/business/channel/channelcontroller/createChannel
/business/channel/channelcontroller/generateChannelCode
/business/channel/channelcontroller/generateChannelInvitation
/business/channel/channelcontroller/updateChannel
/business/channel/channelcontroller/updateChannelLoanRules
/business/channel/channelcontroller/listChannels
/business/common/message/read
/business/user/account/upavatar
/business/user/account/upuserinfo
/business/user/account/changepwd
/business/user/data/changePassword
/business/user/data/checkPassword
/business/user/data/saveInfo
/business/user/refreshtoken
/business/user/registerUser
/business/user/resetPassword
/business/user/info/latestActivity
/business/user/info/projectList
/business/user/info/teamList
/business/user/login
/business/user/logout
/business/user/postSms
/business/order/manager/claimOrder
/business/order/manager/closeOrder
/business/order/manager/cancelClaimOrder
/business/order/manager/createOrderRemark
/business/order/manager/updateBillDueDate
/business/order/manager/updateOrderChannel
/business/order/manager/assignOrder
/business/order/manager/earlySettlement
/business/order/manager/listOrders
/business/order/manager/processDisbursement
/business/order/manager/waiveBillAmount
/business/system/role/save
/business/system/role/upStatus
/business/system/rule/save
/business/system/rule/upStatus
/business/system/account/isaccountexist
/business/system/account/save
/business/system/account/upStatus
/business/system/dept/upStatus
/business/system/dept/upgrouppid
/business/system/dept/save
/business/suggestions/complaint/handleComplaint
/business/payment/manager/processPartialOfflinePayment
/business/payment/manager/processRefund
/business/payment/manager/paymentCallback
/business/payment/manager/calculateRepaymentSchedule
/business/payment/manager/cancelOfflinePayment
/business/payment/manager/refreshRefundStatus
/business/payment/manager/refundCallback
/business/payment/manager/disbursementCallback
/business/productrules/list/createRule
/business/productrules/list/updateRule
/business/makecode/product/save
/business/makecode/product/upStatus
/business/makecode/productcate/save
/business/makecode/productcate/upStatus
/business/makecode/cate/save
/business/makecode/cate/upStatus
/business/bankcard/bankcardcontroller/postBindBankCard
/business/bankcard/bankcardcontroller/postBindBankCardSms
/business/bankcard/bankcardcontroller/postBindInfo
/business/repayment/manager/manualWithhold
/business/repayment/manager/paymentCallback
/uniapp/user/complaint/uploadComplaintContent
/uniapp/user/cancelAccount
/uniapp/user/login
/uniapp/user/logout
/uniapp/user/postBySms
/uniapp/user/postSms
/uniapp/user/refreshtoken
/uniapp/bankcard/bankcardcontroller/postBindBankCard
/uniapp/bankcard/bankcardcontroller/postBindBankCardSms
/uniapp/bankcard/bankcardcontroller/postBindInfo
/uniapp/hetong/createContract
/uniapp/hetong/queryContractStatus
/uniapp/hetong/updateContract
/uniapp/identity/postFaceAuth
/uniapp/identity/postIdentity
/uniapp/order/checkCanCreate
/uniapp/order/createOrder
/uniapp/common/uploadfile/onefile
/uniapp/repayment/paymentcontroller/createRepayment
/common/upload/file
/common/upload/image
/common/upload/thirdImage
/common/uploadfile/onefile
/common/attupfile/upfile
/common/install/save
/common/table/weigh
