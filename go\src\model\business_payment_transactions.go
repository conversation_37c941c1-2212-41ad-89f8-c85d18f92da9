package model

import (
	"context"
	"fmt"
	"time"

	"fincore/utils/gform"
)

// BusinessPaymentTransactions 支付交易流水数据模型
type BusinessPaymentTransactions struct {
	ID                          int        `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`                         // 主键ID，自增
	TransactionNo               string     `json:"transaction_no" db:"transaction_no"`                                 // 交易流水号，系统内部唯一标识
	ThirdPartyOrderNo           string     `json:"third_party_order_no" db:"third_party_order_no"`                     // 第三方订单号，第三方支付平台的订单号
	ChannelTransactionNo        string     `json:"channel_transaction_no" db:"channel_transaction_no"`                 // 渠道交易流水号，第三方支付平台返回的流水号，可为空
	RelatedTransactionNo        *string    `json:"related_transaction_no" db:"related_transaction_no"`                 // 关联交易流水号，用于退款等关联交易，可为空
	OrderID                     int        `json:"order_id" db:"order_id"`                                             // 订单ID，关联贷款订单表
	OrderNo                     string     `json:"order_no" db:"order_no"`                                             // 订单号，关联贷款订单表
	BillID                      *int       `json:"bill_id" db:"bill_id"`                                               // 账单ID，关联还款账单表，可为空（放款时为空）
	UserID                      int        `json:"user_id" db:"user_id"`                                               // 用户ID，关联用户表
	BankCardID                  *int       `json:"bank_card_id" db:"bank_card_id"`                                     // 银行卡ID，关联银行卡表，可为空
	PaymentChannelID            int        `json:"payment_channel_id" db:"payment_channel_id"`                         // 支付渠道ID，关联支付渠道表
	Type                        string     `json:"type" db:"type"`                                                     // 交易类型：DISBURSEMENT-放款，REPAYMENT-用户主动还款，WITHHOLD-系统代扣，REFUND-退款，PARTIAL_OFFLINE_REPAYMENT-部分线下还款，MANUAL_WITHHOLD-管理员手动代扣
	WithholdType                *string    `json:"withhold_type" db:"withhold_type"`                                   // 代扣类型：ASSET-资产代扣，GUARANTEE-担保代扣，可为空
	Amount                      Decimal    `json:"amount,string" db:"amount"`                                          // 交易金额，单位：元
	Status                      int        `json:"status" db:"status"`                                                 // 交易状态：状态:  0-待提交； 1-已提交；2-处理成功；3-处理失败；4-已撤回
	ErrorCode                   string     `json:"error_code" db:"error_code"`                                         // 错误代码，默认为空字符串
	ErrorMessage                string     `json:"error_message" db:"error_message"`                                   // 错误信息，默认为空字符串
	CallbackResult              *string    `json:"callback_result" db:"callback_result"`                               // 回调结果 json 格式，可为空
	OfflinePaymentChannelDetail *string    `json:"offline_payment_channel_detail" db:"offline_payment_channel_detail"` // 线下支付渠道，可为空
	OfflinePaymentVoucher       *string    `json:"offline_payment_voucher" db:"offline_payment_voucher"`               // 线下支付凭证，可为空
	Remark                      *string    `json:"remark" db:"remark"`                                                 // 备注信息，退款时存储退款原因，可为空
	CreatedAt                   time.Time  `json:"created_at" db:"created_at"`                                         // 创建时间，Unix时间戳
	CompletedAt                 *time.Time `json:"completed_at" db:"completed_at"`                                     // 完成时间，Unix时间戳，可为空
}

const (
	TransactionStatusPending   = 0 // 待提交
	TransactionStatusSubmitted = 1 // 已提交
	TransactionStatusSuccess   = 2 // 处理成功
	TransactionStatusFailed    = 3 // 处理失败
	TransactionStatusCanceled  = 4 // 已撤回
)

var TransactionStatusDescriptions = map[int]string{
	TransactionStatusPending:   "待提交",
	TransactionStatusSubmitted: "已提交",
	TransactionStatusSuccess:   "处理成功",
	TransactionStatusFailed:    "处理失败",
	TransactionStatusCanceled:  "已撤回",
}

var WithholdTypeDescriptions = map[string]string{
	"ASSET":     "统统付资管支付",
	"GUARANTEE": "统统付担保支付",
}

const (
	TransactionTypeDisbursement            = "DISBURSEMENT"              // 放款
	TransactionTypeRefund                  = "REFUND"                    // 退款
	TransactionTypePartialOfflineRepayment = "PARTIAL_OFFLINE_REPAYMENT" // 部分线下还款
	TransactionTypeRepayment               = "REPAYMENT"                 // 用户主动还款
	TransactionTypeWithhold                = "WITHHOLD"                  // 系统代扣
	TransactionTypeManualWithhold          = "MANUAL_WITHHOLD"           // 管理员手动代扣
)

// TableName 指定表名
func (BusinessPaymentTransactions) TableName() string {
	return "business_payment_transactions"
}

// BusinessPaymentTransactionsService 支付交易流水服务
type BusinessPaymentTransactionsService struct {
	ctx context.Context
}

// NewBusinessPaymentTransactionsService 创建支付交易流水服务实例
func NewBusinessPaymentTransactionsService(ctx context.Context) *BusinessPaymentTransactionsService {
	return &BusinessPaymentTransactionsService{
		ctx: ctx,
	}
}

// GetTransactionStatusText 获取交易状态文案
func GetTransactionStatusText(status int) string {
	return TransactionStatusDescriptions[status]
}

// GetWithholdTypeText 获取代扣类型文案
func GetWithholdTypeText(withholdType string) string {
	return WithholdTypeDescriptions[withholdType]
}

// CreateTransaction 创建支付交易流水
func (s *BusinessPaymentTransactionsService) CreateTransaction(transaction *BusinessPaymentTransactions) error {
	if transaction == nil {
		return fmt.Errorf("交易流水对象不能为空")
	}

	// 设置创建时间
	if transaction.CreatedAt.IsZero() {
		transaction.CreatedAt = time.Now()
	}

	// 构建插入数据
	data := map[string]interface{}{
		"transaction_no":                 transaction.TransactionNo,
		"channel_transaction_no":         transaction.ChannelTransactionNo,
		"third_party_order_no":           transaction.ThirdPartyOrderNo,
		"related_transaction_no":         transaction.RelatedTransactionNo,
		"order_id":                       transaction.OrderID,
		"order_no":                       transaction.OrderNo,
		"bill_id":                        transaction.BillID,
		"user_id":                        transaction.UserID,
		"bank_card_id":                   transaction.BankCardID,
		"payment_channel_id":             transaction.PaymentChannelID,
		"type":                           transaction.Type,
		"withhold_type":                  transaction.WithholdType,
		"amount":                         transaction.Amount,
		"status":                         transaction.Status,
		"error_code":                     transaction.ErrorCode,
		"error_message":                  transaction.ErrorMessage,
		"callback_result":                transaction.CallbackResult,
		"offline_payment_channel_detail": transaction.OfflinePaymentChannelDetail,
		"offline_payment_voucher":        transaction.OfflinePaymentVoucher,
		"remark":                         transaction.Remark,
		"created_at":                     transaction.CreatedAt,
		"completed_at":                   transaction.CompletedAt,
	}

	insertID, err := DB().Table("business_payment_transactions").InsertGetId(data)
	if err != nil {
		return fmt.Errorf("创建支付交易流水失败: %v", err)
	}

	// 设置插入的ID
	transaction.ID = int(insertID)

	return nil
}

// GetTransactionByID 根据ID获取交易记录
func (s *BusinessPaymentTransactionsService) GetTransactionByID(id int) (*BusinessPaymentTransactions, error) {
	data, err := DB().Table("business_payment_transactions").Where("id", id).WhereNull("deleted_at").First()
	if err != nil {
		return nil, fmt.Errorf("查询支付交易流水失败: %v", err)
	}
	if len(data) == 0 {
		return nil, nil
	}

	var transaction BusinessPaymentTransactions
	if err := mapToStruct(data, &transaction); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &transaction, nil
}

// GetTransactionByChannelTransactionNo 根据渠道交易流水号获取交易记录
func (s *BusinessPaymentTransactionsService) GetTransactionByChannelTransactionNo(transactionNo string) (*BusinessPaymentTransactions, error) {
	data, err := DB().Table("business_payment_transactions").Where("channel_transaction_no", transactionNo).WhereNull("deleted_at").First()
	if err != nil {
		return nil, fmt.Errorf("查询支付交易流水失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("支付交易流水不存在")
	}

	var transaction BusinessPaymentTransactions
	if err := mapToStruct(data, &transaction); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &transaction, nil
}

// GetTransactionByNo 根据交易流水号获取交易记录
func (s *BusinessPaymentTransactionsService) GetTransactionByNo(transactionNo string) (*BusinessPaymentTransactions, error) {
	data, err := DB().Table("business_payment_transactions").Where("transaction_no", transactionNo).WhereNull("deleted_at").First()
	if err != nil {
		return nil, fmt.Errorf("查询支付交易流水失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("支付交易流水不存在")
	}

	var transaction BusinessPaymentTransactions
	if err := mapToStruct(data, &transaction); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &transaction, nil
}

type TransactionCondition struct {
	OrderID              int    `json:"order_id"`               // 订单ID
	OrderNo              string `json:"order_no"`               // 订单号
	RelatedTransactionNo string `json:"related_transaction_no"` // 关联交易流水号
	Status               []any  `json:"status"`                 // 交易状态
	Type                 string `json:"type"`                   // 交易类型
}

// GetTransactionsByConditions 根据条件获取交易流水列表
func (s *BusinessPaymentTransactionsService) GetTransactionsByConditions(condition TransactionCondition) ([]BusinessPaymentTransactions, error) {
	query := DB().Table("business_payment_transactions").WhereNull("deleted_at")
	if condition.OrderID != 0 {
		query = query.Where("order_id", condition.OrderID)
	}
	if condition.OrderNo != "" {
		query = query.Where("order_no", condition.OrderNo)
	}

	if condition.RelatedTransactionNo != "" {
		query = query.Where("related_transaction_no", condition.RelatedTransactionNo)
	}

	if len(condition.Status) > 0 {
		query = query.WhereIn("status", condition.Status)
	}

	if condition.Type != "" {
		query = query.Where("type", condition.Type)
	}

	query = query.OrderBy("created_at DESC")
	data, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询支付交易流水失败: %v", err)
	}

	var transactions []BusinessPaymentTransactions
	for _, item := range data {
		var transaction BusinessPaymentTransactions
		if err := mapToStruct(item, &transaction); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		transactions = append(transactions, transaction)
	}

	return transactions, nil
}

// UpdateTransactionStatusParams 更新交易状态参数
type UpdateTransactionStatusParams struct {
	TransactionNo        string     `json:"transaction_no" db:"transaction_no"`
	Status               int        `json:"status" db:"status"`
	Amount               Decimal    `json:"amount" db:"amount"`
	ChannelTransactionNo string     `json:"channel_transaction_no" db:"channel_transaction_no"`
	ThirdPartyOrderNo    string     `json:"third_party_order_no" db:"third_party_order_no"`
	ErrorCode            string     `json:"error_code" db:"error_code"`
	ErrorMessage         string     `json:"error_message" db:"error_message"`
	CallbackResult       string     `json:"callback_result" db:"callback_result"`
	CompletedAt          *time.Time `json:"completed_at" db:"completed_at"`
}

type UpdateTransactionStatusResultWhere struct {
	ID                   int
	TransactionNo        string
	Status               int
	ChannelTransactionNo string
}

// UpdateTransactionStatus 更新交易状态
func (s *BusinessPaymentTransactionsService) UpdateTransactionStatus(
	tx gform.IOrm,
	where UpdateTransactionStatusResultWhere,
	updateMap map[string]interface{},
) error {

	// 如果状态为成功或失败，设置完成时间
	if status, ok := updateMap["status"]; ok {
		if status == TransactionStatusSuccess || status == TransactionStatusFailed {
			now := time.Now()
			updateMap["completed_at"] = &now
		}
	}

	var up gform.IOrm
	if tx != nil {
		up = tx.Table("business_payment_transactions")
	} else {
		up = DB(WithContext(s.ctx)).Table("business_payment_transactions")
	}

	if where.ID != 0 {
		up = up.Where("id", where.ID)
	}
	if where.TransactionNo != "" {
		up = up.Where("transaction_no", where.TransactionNo)
	}
	if where.Status != 0 {
		up = up.Where("status", where.Status)
	}
	if where.ChannelTransactionNo != "" {
		up = up.Where("channel_transaction_no", where.ChannelTransactionNo)
	}
	affectedRows, err := up.Update(updateMap)
	if err != nil {
		return fmt.Errorf("更新支付交易流水状态失败: %v", err)
	}

	if affectedRows == 0 {
		fmt.Printf("更新操作未影响任何记录，成功: transaction_no=%s\n", where.TransactionNo)
	}

	return nil
}

// GetTransactionsByBillID 根据账单ID获取交易流水列表
func GetTransactionsByBillID(billID int) ([]BusinessPaymentTransactions, error) {
	data, err := DB().Table("business_payment_transactions").Where("bill_id", billID).WhereNull("deleted_at").OrderBy("created_at DESC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询支付交易流水失败: %v", err)
	}

	var transactions []BusinessPaymentTransactions
	for _, item := range data {
		var transaction BusinessPaymentTransactions
		if err := mapToStruct(item, &transaction); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		transactions = append(transactions, transaction)
	}

	return transactions, nil
}

// GetPendingTransactions 获取处理中的交易流水
func (s *BusinessPaymentTransactionsService) GetPendingTransactions() ([]BusinessPaymentTransactions, error) {
	data, err := DB().Table("business_payment_transactions").Where("status", 0).WhereNull("deleted_at").OrderBy("created_at ASC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询处理中的交易流水失败: %v", err)
	}

	var transactions []BusinessPaymentTransactions
	for _, item := range data {
		var transaction BusinessPaymentTransactions
		if err := mapToStruct(item, &transaction); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		transactions = append(transactions, transaction)
	}

	return transactions, nil
}

// CheckTransactionExists 检查指定条件的交易是否存在
func (s *BusinessPaymentTransactionsService) CheckTransactionExists(orderID int, billID *int, transactionType string, date string) (bool, error) {
	query := DB().Table("business_payment_transactions").
		Where("order_id", orderID).
		Where("type", transactionType).
		Where("status", TransactionStatusSubmitted).
		WhereNull("deleted_at").
		Where("DATE(FROM_UNIXTIME(created_at))", date)

	if billID != nil {
		query = query.Where("bill_id", *billID)
	}

	count, err := query.Count()
	if err != nil {
		return false, fmt.Errorf("检查交易是否存在失败: %v", err)
	}

	return count > 0, nil
}

// GetTimeoutSubmittedTransactions 获取超时的已提交交易流水
func (s *BusinessPaymentTransactionsService) GetTimeoutSubmittedTransactions(timeoutThreshold time.Time) ([]BusinessPaymentTransactions, error) {
	data, err := DB().Table("business_payment_transactions").
		Where("status", TransactionStatusSubmitted).
		Where("created_at", "<", timeoutThreshold).
		WhereNull("deleted_at").
		OrderBy("created_at ASC").
		Get()
	if err != nil {
		return nil, fmt.Errorf("查询超时的已提交交易流水失败: %v", err)
	}

	var transactions []BusinessPaymentTransactions
	for _, item := range data {
		var transaction BusinessPaymentTransactions
		if err := mapToStruct(item, &transaction); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		transactions = append(transactions, transaction)
	}

	return transactions, nil
}
