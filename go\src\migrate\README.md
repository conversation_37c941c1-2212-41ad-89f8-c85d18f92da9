### 创建新表
1. 在开发数据库上创建新表
2. 安装gentool     go install gorm.io/gen/tools/gentool@latest
3. cd到migrate目录下生成代码     gentool -dsn "root:root@tcp(192.168.0.20:3306)/fincore?charset=utf8mb4&parseTime=True&loc=Local&timeout=1000ms" -onlyModel -tables "表的名称"
4. 在migrate.go 的Migrate函数中添加新表的迁移任务
```go
// 数据库自动迁移
func Migrate() {
	db, err := initDB()
	if err != nil {
		global.App.Log.Error("数据迁移初始化数据库连接错误，" + err.Error())
	}
	DB = db
	DB.AutoMigrate(
		&model.QuotaNotice{},
		&model.MigrateTimeline{},
        // 在这里添加新表
		&model.新表{},
	)
	migrateTasks = []MigrateTask{
        // 在这里添加新的迁移任务
		&timeline.ExampleMigrateTask{},
	}
	runMigrateTask()
}
```

### 历史数据变更
1. 在migrate/timeline目录下创建新的迁移任务文件, 文件命名规则: 日期_操作表名_操作动作.go  必须唯一
2. 实现MigrateTask接口，copy example.go 内容，修改结构体名称，实现Execute函数
3. 在Migrate函数中添加新的迁移任务


提示： 开发时反复执行需要删除migrate_timeline表中的对应记录