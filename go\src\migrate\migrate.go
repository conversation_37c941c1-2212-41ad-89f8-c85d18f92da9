package migrate

import (
	"fincore/global"
	"fincore/migrate/dao/model"
	"fincore/migrate/timeline"

	"fmt"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

//gentool -dsn "root:root@tcp(192.168.0.20:3306)/fincore?charset=utf8mb4&parseTime=True&loc=Local&timeout=1000ms" -onlyModel -tables "quota_notice"

var (
	DB           *gorm.DB
	migrateTasks []MigrateTask
)

func runMigrateTask() {
	for _, task := range migrateTasks {
		record := task.Name()
		var result model.MigrateTimeline
		mt := model.MigrateTimeline{Record: record}
		DB.Model(mt).First(&result)
		if result.Record == record {
			continue
		}
		global.App.Log.Info("数据迁移任务开始执行，" + task.Name())
		if err := task.Execute(DB); err != nil {
			global.App.Log.Error("数据迁移任务执行失败，" + record + "，" + err.Error())
			continue
		}
		DB.Create(&mt)
		global.App.Log.Info("数据迁移任务执行成功，" + record)
	}
}

// 数据库自动迁移
func Migrate() {
	db, err := initDB()
	if err != nil {
		global.App.Log.Error("数据迁移初始化数据库连接错误，" + err.Error())
	}
	DB = db
	DB.AutoMigrate(
		&model.QuotaNotice{},
		&model.MigrateTimeline{},
	)
	migrateTasks = []MigrateTask{
		&timeline.ExampleMigrateTask{},
	}
	runMigrateTask()
}

// 初始化gorm链接
func initDB() (*gorm.DB, error) {
	dsbSource := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8mb4&parseTime=True&loc=Local&timeout=1000ms", global.App.Config.DBconf.Username, global.App.Config.DBconf.Password, global.App.Config.DBconf.Hostname, global.App.Config.DBconf.Hostport, global.App.Config.DBconf.Database)

	return gorm.Open(mysql.Open(dsbSource), &gorm.Config{
		// 不自动创建外键
		DisableForeignKeyConstraintWhenMigrating: true,
	})
}

type MigrateTask interface {
	Execute(db *gorm.DB) error
	Name() string
}
