package statistics

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"
	"fincore/utils/pagination"
	"fincore/utils/shopspringutils"

	"github.com/golang-module/carbon/v2"
	"golang.org/x/sync/errgroup"
)

// Service 渠道统计服务层
type Service struct {
	ctx        context.Context
	repository *Repository
	logger     *log.Logger
}

// NewService 创建渠道统计服务实例
func NewService(ctx context.Context) *Service {
	return &Service{
		ctx:        ctx,
		repository: NewRepository(ctx),
		logger:     log.RegisterModule("statistics", "统计服务"),
	}
}

// ExecuteChannelStatistics 执行渠道统计
// 这是主要的统计执行方法，会统计所有启用渠道的当天数据
func (s *Service) ExecuteChannelStatistics() error {
	s.logger.Info("开始执行渠道统计任务")

	// 获取今天的时间范围
	startTime, endTime := GetTodayTimeRange()
	s.logger.Info("统计时间范围",
		log.String("start_time", FormatTimeForLog(startTime)),
		log.String("end_time", FormatTimeForLog(endTime)),
	)

	// 获取所有启用的渠道
	channels, err := s.repository.GetEnabledChannels()
	if err != nil {
		s.logger.Error("获取启用渠道列表失败", log.String("error", err.Error()))
		return fmt.Errorf("获取启用渠道列表失败: %v", err)
	}

	if len(channels) == 0 {
		s.logger.Warn("没有找到启用的渠道")
		return nil
	}

	s.logger.Info("找到启用渠道", log.Int("channel_count", len(channels)))

	// 使用并发处理多个渠道的统计
	var wg sync.WaitGroup
	errChan := make(chan error, len(channels))

	for _, channelData := range channels {
		wg.Add(1)
		go func(data gform.Data) {
			defer wg.Done()

			channelInfo, err := ConvertChannelData(data)
			if err != nil {
				errChan <- fmt.Errorf("转换渠道数据失败: %v", err)
				return
			}

			err = s.statisticsForChannel(channelInfo.ID, channelInfo.ChannelName, startTime, endTime)
			if err != nil {
				errChan <- fmt.Errorf("渠道[%s]统计失败: %v", channelInfo.ChannelName, err)
				return
			}
		}(channelData)
	}

	// 等待所有goroutine完成
	wg.Wait()
	close(errChan)

	// 检查是否有错误
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
		s.logger.Error("渠道统计出现错误", log.String("error", err.Error()))
	}

	if len(errors) > 0 {
		return fmt.Errorf("部分渠道统计失败，错误数量: %d", len(errors))
	}

	s.logger.Info("渠道统计任务执行完成",
		log.Int("success_channel_count", len(channels)),
	)

	return nil
}

// statisticsForChannel 为单个渠道执行统计
func (s *Service) statisticsForChannel(channelID uint, channelName string, startTime, endTime time.Time) error {
	s.logger.Info("开始统计渠道数据",
		log.Uint("channel_id", channelID),
		log.String("channel_name", channelName),
	)

	// 统计新用户数
	newUserCount, err := s.repository.GetNewUserCountByChannel(channelID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("统计新用户数失败: %v", err)
	}

	// 统计实名通过数
	realNameCount, err := s.repository.GetRealNameCountByChannel(channelID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("统计实名通过数失败: %v", err)
	}

	// 统计成交数
	transactionCount, err := s.repository.GetTransactionCountByChannel(channelID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("统计成交数失败: %v", err)
	}

	s.logger.Info("渠道统计数据",
		log.Uint("channel_id", channelID),
		log.String("channel_name", channelName),
		log.Uint("new_user_count", newUserCount),
		log.Uint("real_name_count", realNameCount),
		log.Uint("transaction_count", transactionCount),
	)

	// 保存统计数据
	date := carbon.CreateFromStdTime(startTime).StartOfDay()
	err = s.repository.SaveChannelStatistics(channelID, newUserCount, realNameCount, transactionCount, *date)
	if err != nil {
		return fmt.Errorf("保存统计数据失败: %v", err)
	}

	s.logger.Info("渠道统计完成",
		log.Uint("channel_id", channelID),
		log.String("channel_name", channelName),
	)

	return nil
}

// ExecuteChannelStatisticsForDate 执行指定日期的渠道统计
// 用于补充统计或重新统计某一天的数据
func (s *Service) ExecuteChannelStatisticsForDate(date carbon.Carbon) error {
	s.logger.Info("开始执行指定日期的渠道统计",
		log.String("date", date.Format("Y-m-d")),
	)

	startTime, endTime := GetSpecificDateTimeRange(date.StdTime())

	// 获取所有启用的渠道
	channels, err := s.repository.GetEnabledChannels()
	if err != nil {
		return fmt.Errorf("获取启用渠道列表失败: %v", err)
	}

	for _, channelData := range channels {
		channelInfo, err := ConvertChannelData(channelData)
		if err != nil {
			s.logger.Error("转换渠道数据失败", log.String("error", err.Error()))
			continue
		}

		err = s.statisticsForChannel(channelInfo.ID, channelInfo.ChannelName, startTime, endTime)
		if err != nil {
			s.logger.Error("渠道统计失败",
				log.String("channel_name", channelInfo.ChannelName),
				log.String("error", err.Error()),
			)
			continue
		}
	}

	s.logger.Info("指定日期的渠道统计任务执行完成",
		log.String("date", date.Format("Y-m-d")),
	)

	return nil
}

// GetStatisticsSummary 获取统计摘要信息
func (s *Service) GetStatisticsSummary() (map[string]interface{}, error) {
	channels, err := s.repository.GetEnabledChannels()
	if err != nil {
		return nil, err
	}

	summary := map[string]interface{}{
		"total_channels":    len(channels),
		"execution_time":    carbon.Now().Format("Y-m-d H:i:s"),
		"time_range":        GetTimeRangeDescription(GetTodayTimeRange()),
		"is_execution_time": IsExecutionTime(),
	}

	return summary, nil
}

// GetChannelStatisticsList 获取渠道统计列表
func (s *Service) GetChannelStatisticsList(params map[string]interface{}) (*pagination.PaginationResponse, error) {
	s.logger.Info("开始获取渠道统计列表", log.Any("params", params))

	// 构建分页请求参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "page_size", 10)

	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 调用repository层获取数据
	result, err := s.repository.GetChannelStatisticsListByParams(params, paginationReq)
	if err != nil {
		s.logger.Error("获取渠道统计列表失败", log.String("error", err.Error()))
		return nil, fmt.Errorf("获取渠道统计列表失败: %v", err)
	}

	// 格式化时间字段
	s.formatTimeFields(result)

	s.logger.Info("获取渠道统计列表成功",
		log.Int64("total", result.Total),
		log.Int("page", result.Page),
		log.Int("pageSize", result.PageSize),
	)

	return result, nil
}

// formatTimeFields 格式化时间字段
func (s *Service) formatTimeFields(result *pagination.PaginationResponse) {
	if result == nil || result.Data == nil {
		return
	}

	if dataSlice, ok := result.Data.([]gform.Data); ok {
		for i, item := range dataSlice {
			if createdAt, exists := item["created_at"]; exists {
				if timeStr, ok := createdAt.(string); ok {
					// 解析时间字符串
					if parsedTime, err := time.Parse(time.RFC3339, timeStr); err == nil {
						// 格式化为 yyyy-mm-dd HH:ii:ss 格式
						item["created_at"] = parsedTime.Format("2006-01-02 15:04:05")
					}
				} else if timeVal, ok := createdAt.(time.Time); ok {
					// 如果是time.Time类型，直接格式化
					item["created_at"] = timeVal.Format("2006-01-02 15:04:05")
				}
			}
			dataSlice[i] = item
		}
		result.Data = dataSlice
	}
}

type GetHomeStatisticsResponse struct {
	DisbursementAmount        float64 `json:"disbursement_amount"`         // 放款金额（本金+利息+担保）
	DisbursementCustomerCount int     `json:"disbursement_customer_count"` // 放款客户数
	DisbursementOrderCount    int     `json:"disbursement_order_count"`    // 放款订单
	DueAmount                 float64 `json:"due_amount"`                  // 到期金额（本金+利息+担保）
	DueRepaymentAmount        float64 `json:"due_repayment_amount"`        // 到期回款总额（本息 + 担保）
	DueRepaymentRate          float64 `json:"due_repayment_rate"`          // 到期回款率 （回款总额/到期金额）
	DueFundsRecoveryRate      float64 `json:"due_funds_recovery_rate"`     // 到期资金回收率（回款总额/本金）
	OverdueCustomerCount      int     `json:"overdue_customer_count"`      // 逾期客户
	OverdueAmount             float64 `json:"overdue_amount"`              // 逾期总额（本息+担保）
}

// GetTrendStatisticsResponse 趋势统计响应结构
type GetTrendStatisticsResponse struct {
	TrendData []TrendDataPoint `json:"trend_data"` // 趋势数据列表
}

// GetHomeStatistics 首页数据统计
// 默认统计所有数据，支持统计指定日期
func (s *Service) GetHomeStatistics(date string) (resp GetHomeStatisticsResponse, err error) {
	// 记录开始时间
	startTime := time.Now()
	s.logger.WithFields(
		log.String("date", date),
	).Info("开始获取首页统计数据")

	// 使用errgroup并行获取统计数据
	var disbursementStats *DisbursementStatistics
	var dueStats *DueStatistics
	var overdueStats *OverdueStatistics

	g, _ := errgroup.WithContext(s.ctx)

	// 并行获取放款统计数据
	g.Go(func() error {
		var err error
		disbursementStats, err = s.repository.GetDisbursementStatistics(date)
		if err != nil {
			s.logger.WithFields(
				log.String("date", date),
				log.String("error", err.Error()),
			).Error("获取放款统计数据失败")
			return fmt.Errorf("获取放款统计数据失败: %v", err)
		}
		return nil
	})

	// 并行获取到期统计数据
	g.Go(func() error {
		var err error
		dueStats, err = s.repository.GetDueStatistics(date)
		if err != nil {
			s.logger.WithFields(
				log.String("date", date),
				log.String("error", err.Error()),
			).Error("获取到期统计数据失败")
			return fmt.Errorf("获取到期统计数据失败: %v", err)
		}
		return nil
	})

	// 并行获取逾期统计数据
	g.Go(func() error {
		var err error
		overdueStats, err = s.repository.GetOverdueStatistics(date)
		if err != nil {
			s.logger.WithFields(
				log.String("date", date),
				log.String("error", err.Error()),
			).Error("获取逾期统计数据失败")
			return fmt.Errorf("获取逾期统计数据失败: %v", err)
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		return resp, err
	}

	// 计算到期回款率：到期回款总额 / 到期金额 * 100
	var dueRepaymentRate float64
	if shopspringutils.CompareAmountsWithDecimal(dueStats.DueAmount, 0) > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(dueStats.DueRepaymentAmount, dueStats.DueAmount)
		dueRepaymentRate = shopspringutils.MultiplyAmountsWithDecimal(rate, 100)
	}

	// 计算到期资金回收率：到期回款总额 / 到期本金总额 * 100
	var dueFundsRecoveryRate float64
	if shopspringutils.CompareAmountsWithDecimal(dueStats.DuePrincipalAmount, 0) > 0 {
		rate := shopspringutils.DivideAmountsWithDecimal(dueStats.DueRepaymentAmount, dueStats.DuePrincipalAmount)
		dueFundsRecoveryRate = shopspringutils.MultiplyAmountsWithDecimal(rate, 100)
	}

	// 组装返回结果
	resp = GetHomeStatisticsResponse{
		DisbursementAmount:        disbursementStats.DisbursementAmount,
		DisbursementCustomerCount: disbursementStats.DisbursementCustomerCount,
		DisbursementOrderCount:    disbursementStats.DisbursementOrderCount,
		DueAmount:                 dueStats.DueAmount,
		DueRepaymentAmount:        dueStats.DueRepaymentAmount,
		DueRepaymentRate:          shopspringutils.CeilToTwoDecimal(dueRepaymentRate),
		DueFundsRecoveryRate:      shopspringutils.CeilToTwoDecimal(dueFundsRecoveryRate),
		OverdueCustomerCount:      overdueStats.OverdueCustomerCount,
		OverdueAmount:             overdueStats.OverdueAmount,
	}

	// 记录执行时间和结果
	duration := time.Since(startTime)
	s.logger.WithFields(
		log.String("date", date),
		log.Float64("disbursement_amount", resp.DisbursementAmount),
		log.Int("disbursement_customer_count", resp.DisbursementCustomerCount),
		log.Int("disbursement_order_count", resp.DisbursementOrderCount),
		log.Float64("due_amount", resp.DueAmount),
		log.Float64("due_repayment_amount", resp.DueRepaymentAmount),
		log.Float64("due_repayment_rate", resp.DueRepaymentRate),
		log.Float64("due_funds_recovery_rate", resp.DueFundsRecoveryRate),
		log.Int("overdue_customer_count", resp.OverdueCustomerCount),
		log.Float64("overdue_amount", resp.OverdueAmount),
		log.String("duration", duration.String()),
	).Info("首页统计数据获取完成")

	return resp, nil
}

// GetTrendStatistics 获取趋势统计数据
func (s *Service) GetTrendStatistics(days int) (GetTrendStatisticsResponse, error) {
	// 记录开始时间
	startTime := time.Now()
	s.logger.WithFields(
		log.Int("days", days),
	).Info("开始获取趋势统计数据")

	// 使用errgroup并行获取4种趋势数据
	var disbursementTrend []TrendDataPoint
	var repaymentTrend []TrendDataPoint
	var dueAmountTrend []TrendDataPoint
	var registrationTrend []TrendDataPoint

	g, _ := errgroup.WithContext(s.ctx)

	// 并行获取放款金额趋势数据
	g.Go(func() error {
		var err error
		disbursementTrend, err = s.repository.GetDisbursementTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取放款金额趋势数据失败")
			return fmt.Errorf("获取放款金额趋势数据失败: %v", err)
		}
		return nil
	})

	// 并行获取回款金额趋势数据
	g.Go(func() error {
		var err error
		repaymentTrend, err = s.repository.GetRepaymentTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取回款金额趋势数据失败")
			return fmt.Errorf("获取回款金额趋势数据失败: %v", err)
		}
		return nil
	})

	// 并行获取到期金额趋势数据
	g.Go(func() error {
		var err error
		dueAmountTrend, err = s.repository.GetDueAmountTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取到期金额趋势数据失败")
			return fmt.Errorf("获取到期金额趋势数据失败: %v", err)
		}
		return nil
	})

	// 并行获取注册量趋势数据
	g.Go(func() error {
		var err error
		registrationTrend, err = s.repository.GetRegistrationTrendData(days)
		if err != nil {
			s.logger.WithFields(
				log.Int("days", days),
				log.String("error", err.Error()),
			).Error("获取注册量趋势数据失败")
			return fmt.Errorf("获取注册量趋势数据失败: %v", err)
		}
		return nil
	})

	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return GetTrendStatisticsResponse{}, err
	}

	// 合并趋势数据
	trendDataMap := make(map[string]*TrendDataPoint)

	// 生成完整的日期列表（SQL查询的是从days天前到昨天的数据）
	// 例如：days=7时，查询从7天前到昨天，共7天的数据
	for i := days; i >= 1; i-- {
		date := carbon.Now().SubDays(i).Format("Y-m-d")
		trendDataMap[date] = &TrendDataPoint{
			Date:               date,
			DisbursementAmount: 0,
			RepaymentAmount:    0,
			DueAmount:          0,
			RepaymentRate:      0,
			RegistrationCount:  0,
		}
	}

	// 填充放款金额数据
	for _, data := range disbursementTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.DisbursementAmount = data.DisbursementAmount
		}
	}

	// 填充回款金额数据
	for _, data := range repaymentTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.RepaymentAmount = data.RepaymentAmount
		}
	}

	// 填充到期金额数据
	for _, data := range dueAmountTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.DueAmount = data.DueAmount
		}
	}

	// 填充注册量数据
	for _, data := range registrationTrend {
		if point, exists := trendDataMap[data.Date]; exists {
			point.RegistrationCount = data.RegistrationCount
		}
	}

	// 计算回款率：回款金额 / 到期金额 * 100
	for _, point := range trendDataMap {
		if shopspringutils.CompareAmountsWithDecimal(point.DueAmount, 0) > 0 {
			// 使用shopspringutils计算回款率：回款金额 / 到期金额 * 100
			rate := shopspringutils.DivideAmountsWithDecimal(point.RepaymentAmount, point.DueAmount)
			point.RepaymentRate = shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(rate, 100))
		} else {
			point.RepaymentRate = 0
		}
	}

	// 转换为有序列表（按时间顺序从早到晚）
	var trendData []TrendDataPoint
	for i := days; i >= 1; i-- {
		date := carbon.Now().SubDays(i).Format("Y-m-d")
		if point, exists := trendDataMap[date]; exists {
			trendData = append(trendData, *point)
		}
	}

	// 记录执行时间
	duration := time.Since(startTime)
	s.logger.WithFields(
		log.Int("days", days),
		log.Int("data_points", len(trendData)),
		log.String("duration", duration.String()),
	).Info("趋势统计数据获取完成")

	return GetTrendStatisticsResponse{
		TrendData: trendData,
	}, nil
}
