{"level":"dev.info","ts":"[2025-08-13 09:10:31.675]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.677]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.677]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.677]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.679]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.026]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0260917,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.026]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.034]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0343827,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.034]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.448]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.449]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.449]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.449]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.451]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.032]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0300859,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.032]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.032]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0312338,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.032]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0533752,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.0533752,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.142]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.1418326,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.142]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.142]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.1418326,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.142]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.061]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":9,"duration":0.0610214,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.061]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.061]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0610214,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.061]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":10,"duration":0.0704513,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":8,"duration":0.0714732,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.840]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.841]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.841]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.841]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.843]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.844]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.844]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.844]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.844]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.844]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.845]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.846]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.846]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.847]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.847]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.848]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.848]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.848]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.848]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.848]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.015]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0145264,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.015]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.036]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0357621,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.036]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.352]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.354]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.354]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.354]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.368]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.368]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.395]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0272587,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.395]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.431]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0630295,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.431]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.012]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0118879,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.012]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.038]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0377127,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.038]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.038]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0377127,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.038]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.044]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.0434673,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.044]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.116]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.117]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.117]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.117]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.118]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0251288,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.050]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0501789,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.050]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.562]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.563]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.563]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.563]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.564]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.566]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.566]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.566]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.566]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.038551,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.039]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.088]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.087673,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.088]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.197]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.200]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.200]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.200]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.024]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0234247,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.024]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.057]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0563077,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.057]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.589]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.592]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.592]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.592]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.597]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.598]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.598]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.599]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.599]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.599]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.599]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.599]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.600]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.600]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.600]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.600]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.600]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.600]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.252]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.253]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.253]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.253]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.254]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0247561,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0253049,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.040]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0398126,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.045822,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.046]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.05389,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.05389,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.448]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.449]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.449]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.449]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.450]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.451]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.451]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.451]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.451]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.451]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.451]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.021]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0202874,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.021]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0457745,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.046]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0449851,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.045]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":6,"duration":0.0449829,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.045]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0528134,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0528134,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.0455025,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.045]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.0455025,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.045]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.063]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":10,"duration":0.0630954,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.063]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.063]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.0630954,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.063]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.075]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":12,"duration":0.0741824,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.075]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.076]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":11,"duration":0.075729,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.076]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.061]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":13,"duration":0.0610899,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.061]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":14,"duration":0.0665747,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.754]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.755]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.755]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.755]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.756]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.020]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0207074,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.020]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.022]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0224149,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.022]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.218]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.219]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.222]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.222]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.222]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.222]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.222]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.911]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.912]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.912]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.912]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.913]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.034]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0340905,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.034]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.065]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.0648169,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.065]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.065]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":1,"duration":0.0653219,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.065]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.065]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0653219,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:14:00.065]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:15:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:15:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:15:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0446727,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:15:00.045]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:15:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0457205,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:15:00.046]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.899]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.901]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.901]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.901]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.903]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.904]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.905]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.905]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.905]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.905]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.905]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.023]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.022983,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.023]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.023]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0235476,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.023]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.052]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.0510674,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.052]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.052681,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-13 15:28:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
