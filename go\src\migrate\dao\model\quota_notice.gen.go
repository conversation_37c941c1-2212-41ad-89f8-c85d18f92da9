// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameQuotaNotice = "quota_notice"

// QuotaNotice mapped from table <quota_notice>
type QuotaNotice struct {
	UID        int32     `gorm:"column:uid;primaryKey;autoIncrement:true;comment:用户id" json:"uid"` // 用户id
	Times      int32     `gorm:"column:times;not null;comment:短信提醒次数" json:"times"`                // 短信提醒次数
	NoticeDate time.Time `gorm:"column:noticeDate;not null;comment:上次提醒日期" json:"noticeDate"`      // 上次提醒日期
}

// TableName QuotaNotice's table name
func (*QuotaNotice) TableName() string {
	return TableNameQuotaNotice
}
