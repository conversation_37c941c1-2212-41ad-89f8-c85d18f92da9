{"level":"dev.error","ts":"[2025-08-13 09:10:42.359]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","request_id":"185b2e59a44fdff40400bbea","error_message":"Error 1054 (42S22): Unknown column 'deleted_at' in 'where clause'","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/app/business/statistics.(*Repository).GetRepaymentTrendData\n\tD:/work/code/fincore/go/src/app/business/statistics/repository.go:386\nfincore/app/business/statistics.(*Service).GetTrendStatistics.func2\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:427\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.info","ts":"[2025-08-13 09:10:42.447]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b2e59a44fdff40400bbea","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"109.3902ms","duration_ms":109}
{"level":"dev.info","ts":"[2025-08-13 09:10:42.454]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b2e59a44fdff40400bbea","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"116.2561ms","duration_ms":116}
{"level":"dev.info","ts":"[2025-08-13 09:10:42.455]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b2e59a44fdff40400bbea","sql":"\n\t\tSELECT\n\t\t\tDATE(disbursed_at) as date,\n\t\t\tCOALESCE(SUM(loan_amount), 0) as disbursement_amount\n\t\tFROM business_loan_orders\n\t\tWHERE status IN (?, ?)\n\t\t\tAND disbursed_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND disbursed_at < CURDATE()\n\t\tGROUP BY DATE(disbursed_at)\n\t\tORDER BY date\n\t, [1 3 7]","duration":"116.764ms","duration_ms":116}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"22.0217ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-13 09:11:00.027]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"23.5266ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-13 09:11:35.391]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b2e65f7a6d0aca0e3d615","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"116.5006ms","duration_ms":116}
{"level":"dev.info","ts":"[2025-08-13 09:11:35.413]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b2e65f7a6d0aca0e3d615","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"138.2045ms","duration_ms":138}
{"level":"dev.info","ts":"[2025-08-13 09:11:35.413]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b2e65f7a6d0aca0e3d615","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"138.7149ms","duration_ms":138}
{"level":"dev.info","ts":"[2025-08-13 09:11:35.417]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b2e65f7a6d0aca0e3d615","sql":"\n\t\tSELECT\n\t\t\tDATE(disbursed_at) as date,\n\t\t\tCOALESCE(SUM(loan_amount), 0) as disbursement_amount\n\t\tFROM business_loan_orders\n\t\tWHERE status IN (?, ?)\n\t\t\tAND disbursed_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND disbursed_at < CURDATE()\n\t\tGROUP BY DATE(disbursed_at)\n\t\tORDER BY date\n\t, [1 3 7]","duration":"142.3389ms","duration_ms":142}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"22.9134ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"30.0859ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"52.3575ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-13 09:12:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"51.7236ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.142]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"141.8326ms","duration_ms":141}
{"level":"dev.info","ts":"[2025-08-13 09:13:00.142]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"141.8326ms","duration_ms":141}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"61.0214ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"53.9107ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"61.0214ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-13 09:14:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"64.3625ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.013]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"13.138ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-13 10:53:00.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"35.2579ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.393]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"24.6398ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-13 10:55:01.430]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"61.4831ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.011]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813105600","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"11.0776ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813105600","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"37.7127ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813105600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"37.7127ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-13 10:56:00.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813105600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"43.4673ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"24.4625ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-13 10:57:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"49.5126ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"38.0024ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-13 11:01:00.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"87.1244ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.023]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"22.4124ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-13 11:05:00.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"55.2734ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-13 14:51:52.582]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b40f7bd20d604f30a6584","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?,?,?), [1 0 3 7 9]","duration":"15.4193ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-13 14:51:52.621]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b40f7bd20d604f30a6584","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?), [1 3 9]","duration":"54.9928ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"24.0041ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"24.0041ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.040]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"39.2926ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-13 14:52:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"44.55ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-13 14:52:06.894]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b40faf52e98bc8a863ec2","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?), [1 3 9]","duration":"502.6583ms","duration_ms":502}
{"level":"dev.info","ts":"[2025-08-13 14:52:06.894]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b40faf52e98bc8a863ec2","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?,?,?), [1 0 3 7 9]","duration":"503.2014ms","duration_ms":503}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"53.89ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-13 14:53:00.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"53.89ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"18.9921ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-13 14:55:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"45.1247ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813145600","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"44.9851ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813145600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"44.479ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813145600","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"52.8134ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-13 14:56:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250813145600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"52.8134ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"45.5025ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-13 14:57:00.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"45.5025ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"63.0954ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"62.9141ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.075]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"74.1824ms","duration_ms":74}
{"level":"dev.info","ts":"[2025-08-13 14:58:00.075]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"75.1923ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"61.01ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-13 14:59:00.065]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"65.6027ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-08-13 15:02:17.581]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b418941bccc7400f53b26","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?), [1 3 9]","duration":"20.1029ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-13 15:02:27.133]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b418941bccc7400f53b26","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?,?,?), [1 0 3 7 9]","duration":"9.5718941s","duration_ms":9571}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"18.7876ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-13 15:03:00.021]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"20.714ms","duration_ms":20}
