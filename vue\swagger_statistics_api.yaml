openapi: 3.0.0
info:
  title: FinCore 统计管理 API
  description: FinCore系统统计管理模块的完整API接口文档，包含首页统计、渠道统计、趋势统计等功能
  version: 1.0.0
  contact:
    name: FinCore Team
    email: <EMAIL>

servers:
  - url: http://localhost:8108
    description: 统计API服务器（开发环境）
  - url: https://api.fincore.com
    description: 统计API服务器（生产环境）

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码，0为成功，1为失败
          example: 0
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
        exdata:
          type: object
          description: 扩展数据
        token:
          type: string
          description: 刷新后的token（如果有）
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567
      required:
        - code
        - message
        - time

    # 首页统计相关schemas
    HomeStatisticsData:
      type: object
      description: 首页统计数据
      properties:
        disbursement_amount:
          type: number
          format: float
          description: 放款金额（本金+利息+担保）
          example: 150000.00
        disbursement_customer_count:
          type: integer
          description: 放款客户数
          example: 25
        disbursement_order_count:
          type: integer
          description: 放款订单数
          example: 30
        due_amount:
          type: number
          format: float
          description: 到期金额（本金+利息+担保）
          example: 120000.00
        due_repayment_amount:
          type: number
          format: float
          description: 到期回款总额（本息+担保）
          example: 108000.00
        due_repayment_rate:
          type: number
          format: float
          description: 到期回款率（回款总额/到期金额）
          example: 90.00
        due_funds_recovery_rate:
          type: number
          format: float
          description: 到期资金回收率（回款总额/本金）
          example: 85.50
        overdue_customer_count:
          type: integer
          description: 逾期客户数
          example: 5
        overdue_amount:
          type: number
          format: float
          description: 逾期总额（本息+担保）
          example: 12000.00
      required:
        - disbursement_amount
        - disbursement_customer_count
        - disbursement_order_count
        - due_amount
        - due_repayment_amount
        - due_repayment_rate
        - due_funds_recovery_rate
        - overdue_customer_count
        - overdue_amount

    HomeStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/HomeStatisticsData'

    # 趋势统计相关schemas
    TrendDataPoint:
      type: object
      description: 趋势数据点
      properties:
        date:
          type: string
          description: 日期（YYYY-MM-DD格式）
          example: "2024-08-12"
        disbursement_amount:
          type: number
          format: float
          description: 放款金额
          example: 50000.00
        repayment_amount:
          type: number
          format: float
          description: 回款金额
          example: 45000.00
        repayment_rate:
          type: number
          format: float
          description: 回款率（百分比）
          example: 90.00
        registration_count:
          type: integer
          description: 注册量
          example: 25
      required:
        - date
        - disbursement_amount
        - repayment_amount
        - repayment_rate
        - registration_count

    TrendStatisticsData:
      type: object
      description: 趋势统计数据
      properties:
        trend_data:
          type: array
          description: 趋势数据列表
          items:
            $ref: '#/components/schemas/TrendDataPoint'
      required:
        - trend_data

    TrendStatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/TrendStatisticsData'

    # 渠道统计相关schemas
    ChannelStatisticsItem:
      type: object
      description: 渠道统计数据项
      properties:
        id:
          type: integer
          description: 统计记录ID
          example: 1
        channel_id:
          type: integer
          description: 渠道ID
          example: 3
        channel_name:
          type: string
          description: 渠道名称
          example: "test"
        channel_code:
          type: string
          description: 渠道编码
          example: "NZKMYUHLPO"
        new_customer_reg_num:
          type: integer
          description: 新用户注册数
          example: 5
        real_name_num:
          type: integer
          description: 实名认证数
          example: 4
        number_of_transactions:
          type: integer
          description: 交易笔数
          example: 2
        created_at:
          type: string
          description: 统计时间（记录创建时间）
          example: "2024-08-04 12:00:00"
      required:
        - id
        - channel_id
        - channel_name
        - channel_code
        - new_customer_reg_num
        - real_name_num
        - number_of_transactions
        - created_at

    ChannelStatisticsPaginationResponse:
      type: object
      description: 渠道统计分页响应
      properties:
        total:
          type: integer
          description: 总记录数
          example: 5
        page:
          type: integer
          description: 当前页码
          example: 1
        page_size:
          type: integer
          description: 每页数量
          example: 10
        total_pages:
          type: integer
          description: 总页数
          example: 1
        has_next:
          type: boolean
          description: 是否有下一页
          example: false
        has_prev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 渠道统计数据列表
          items:
            $ref: '#/components/schemas/ChannelStatisticsItem'
      required:
        - total
        - page
        - pageSize
        - totalPages
        - hasNext
        - hasPrev
        - data

    ChannelStatisticsListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ChannelStatisticsPaginationResponse'

paths:
  # 首页统计接口
  /business/statistics/statisticscontroller/getHomeStatistics:
    get:
      tags:
        - 统计管理
      summary: 获取首页数据统计
      description: |
        获取首页展示的核心统计数据，包括放款、回款、逾期等关键业务指标。

        **业务说明：**
        - 当date参数为空时，返回累计统计数据
        - 当date参数有值时，返回指定日期的统计数据
        - 逾期统计：当指定日期时，返回截止到该日期的累计逾期数据
        - 所有金额计算使用高精度decimal库确保准确性
        - 使用并行查询优化性能

        **数据逻辑：**
        - 放款金额：已放款订单的总金额（本金+利息+担保）
        - 放款客户数：有放款记录的唯一客户数
        - 放款订单数：已放款的订单总数
        - 到期金额：应还款账单的总金额（本金+利息+担保）
        - 到期回款总额：已支付账单的总金额
        - 到期回款率：回款总额/到期金额×100%
        - 到期资金回收率：回款总额/放款本金×100%
        - 逾期客户数：有逾期账单的唯一客户数
        - 逾期总额：逾期账单的未还金额

        **权限要求：**
        - 需要登录用户权限
        - 需要首页统计查看权限
      parameters:
        - name: date
          in: query
          description: 统计日期（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-12"
      responses:
        '200':
          description: 获取首页统计数据成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HomeStatisticsResponse'
              examples:
                with_date:
                  summary: 指定日期统计数据响应示例
                  value:
                    code: 0
                    message: "获取首页统计数据成功"
                    data:
                      disbursement_amount: 150000.00
                      disbursement_customer_count: 25
                      disbursement_order_count: 30
                      due_amount: 120000.00
                      due_repayment_amount: 108000.00
                      due_repayment_rate: 90.00
                      due_funds_recovery_rate: 85.50
                      overdue_customer_count: 5
                      overdue_amount: 12000.00
                    exdata: null
                    token: ""
                    time: 1754292247
                without_date:
                  summary: 累计统计数据响应示例
                  value:
                    code: 0
                    message: "获取首页统计数据成功"
                    data:
                      disbursement_amount: 2500000.00
                      disbursement_customer_count: 450
                      disbursement_order_count: 520
                      due_amount: 2200000.00
                      due_repayment_amount: 1980000.00
                      due_repayment_rate: 90.00
                      due_funds_recovery_rate: 85.50
                      overdue_customer_count: 35
                      overdue_amount: 220000.00
                    exdata: null
                    token: ""
                    time: 1754292247
                no_data:
                  summary: 无数据响应示例
                  value:
                    code: 0
                    message: "获取首页统计数据成功"
                    data:
                      disbursement_amount: 0.00
                      disbursement_customer_count: 0
                      disbursement_order_count: 0
                      due_amount: 0.00
                      due_repayment_amount: 0.00
                      due_repayment_rate: 0.00
                      due_funds_recovery_rate: 0.00
                      overdue_customer_count: 0
                      overdue_amount: 0.00
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_date:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取首页统计数据失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247
                calculation_error:
                  summary: 计算错误
                  value:
                    code: 1
                    message: "获取首页统计数据失败"
                    data: "统计数据计算失败"
                    exdata: null
                    token: ""
                    time: 1754292247

  # 趋势统计接口
  /business/statistics/statisticscontroller/getTrendStatistics:
    get:
      tags:
        - 统计管理
      summary: 获取首页统计趋势数据
      description: |
        获取首页展示的趋势统计数据，包括放款、回款、回款率、注册量的时间序列数据。

        **业务说明：**
        - 支持查询近7日或近30日的趋势数据
        - 每天一个数据点，返回完整的时间序列
        - 包含4个核心趋势：放款金额、回款金额、回款率、注册量
        - 使用并行查询优化性能
        - 所有金额计算使用高精度decimal库确保准确性
        - 自动填充缺失日期的数据点（值为0）

        **数据逻辑：**
        - 放款金额：当日成功放款的订单总金额
        - 回款金额：当日已支付的账单总金额
        - 回款率：当日回款金额/放款金额×100%
        - 注册量：当日新注册的用户数量

        **权限要求：**
        - 需要登录用户权限
        - 需要首页统计查看权限
      parameters:
        - name: days
          in: query
          description: 查询天数，支持7日或30日
          required: true
          schema:
            type: integer
            enum: [7, 30]
            example: 7
      responses:
        '200':
          description: 获取趋势统计数据成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrendStatisticsResponse'
              examples:
                seven_days:
                  summary: 近7日趋势数据响应示例
                  value:
                    code: 0
                    message: "获取趋势统计数据成功"
                    data:
                      trend_data:
                        - date: "2024-08-06"
                          disbursement_amount: 45000.00
                          repayment_amount: 40000.00
                          repayment_rate: 88.89
                          registration_count: 20
                        - date: "2024-08-07"
                          disbursement_amount: 50000.00
                          repayment_amount: 45000.00
                          repayment_rate: 90.00
                          registration_count: 25
                        - date: "2024-08-08"
                          disbursement_amount: 55000.00
                          repayment_amount: 50000.00
                          repayment_rate: 90.91
                          registration_count: 30
                        - date: "2024-08-09"
                          disbursement_amount: 48000.00
                          repayment_amount: 43000.00
                          repayment_rate: 89.58
                          registration_count: 22
                        - date: "2024-08-10"
                          disbursement_amount: 52000.00
                          repayment_amount: 47000.00
                          repayment_rate: 90.38
                          registration_count: 28
                        - date: "2024-08-11"
                          disbursement_amount: 49000.00
                          repayment_amount: 44000.00
                          repayment_rate: 89.80
                          registration_count: 24
                        - date: "2024-08-12"
                          disbursement_amount: 51000.00
                          repayment_amount: 46000.00
                          repayment_rate: 90.20
                          registration_count: 26
                    exdata: null
                    token: ""
                    time: 1754292247
                no_data:
                  summary: 无数据响应示例
                  value:
                    code: 0
                    message: "获取趋势统计数据成功"
                    data:
                      trend_data:
                        - date: "2024-08-06"
                          disbursement_amount: 0.00
                          repayment_amount: 0.00
                          repayment_rate: 0.00
                          registration_count: 0
                        - date: "2024-08-07"
                          disbursement_amount: 0.00
                          repayment_amount: 0.00
                          repayment_rate: 0.00
                          registration_count: 0
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_days:
                  summary: 天数参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "天数参数必须是7或30"
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取趋势统计数据失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247
                calculation_error:
                  summary: 计算错误
                  value:
                    code: 1
                    message: "获取趋势统计数据失败"
                    data: "趋势数据计算失败"
                    exdata: null
                    token: ""
                    time: 1754292247

  # 渠道统计接口
  /business/statistics/statisticscontroller/getChannelStatistics:
    get:
      tags:
        - 统计管理
      summary: 获取渠道统计列表
      description: |
        获取渠道维度的统计数据列表，支持分页和日期筛选。

        **业务说明：**
        - 查询channel_statistics表数据并关联channel表获取渠道信息
        - 支持按日期筛选统计数据
        - 返回数据包含：渠道信息、新用户注册数、实名认证数、交易笔数等
        - 按统计时间倒序排列

        **权限要求：**
        - 需要登录用户权限
        - 需要渠道统计查看权限
      parameters:
        - name: page
          in: query
          description: 页码（从1开始）
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: date
          in: query
          description: 统计日期筛选（YYYY-MM-DD格式，可选）
          required: false
          schema:
            type: string
            pattern: '^\\d{4}-\\d{2}-\\d{2}$'
            example: "2024-08-04"
      responses:
        '200':
          description: 获取渠道统计列表成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelStatisticsListResponse'
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    code: 0
                    message: "获取渠道统计列表成功"
                    data:
                      total: 3
                      page: 1
                      pageSize: 10
                      totalPages: 1
                      hasNext: false
                      hasPrev: false
                      data:
                        - id: 3
                          channel_id: 3
                          channel_name: "test"
                          channel_code: "NZKMYUHLPO"
                          new_customer_reg_num: 5
                          real_name_num: 4
                          number_of_transactions: 2
                          created_at: "2024-08-04 12:00:00"
                        - id: 2
                          channel_id: 2
                          channel_name: "公众号"
                          channel_code: "GZH8F5N3P7"
                          new_customer_reg_num: 15
                          real_name_num: 12
                          number_of_transactions: 8
                          created_at: "2024-08-04 11:00:00"
                        - id: 1
                          channel_id: 1
                          channel_name: "fincore"
                          channel_code: "HS2K9X7Q1M"
                          new_customer_reg_num: 10
                          real_name_num: 8
                          number_of_transactions: 5
                          created_at: "2024-08-04 10:00:00"
                    exdata: null
                    token: ""
                    time: 1754292247
                empty:
                  summary: 空数据响应示例
                  value:
                    code: 0
                    message: "获取渠道统计列表成功"
                    data:
                      total: 0
                      page: 1
                      pageSize: 10
                      totalPages: 0
                      hasNext: false
                      hasPrev: false
                      data: []
                    exdata: null
                    token: ""
                    time: 1754292247
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                invalid_date:
                  summary: 日期格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "日期格式错误，请使用YYYY-MM-DD格式"
                    exdata: null
                    token: ""
                    time: 1754292247
                invalid_page:
                  summary: 页码参数错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "页码必须大于0"
                    exdata: null
                    token: ""
                    time: 1754292247
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              examples:
                server_error:
                  summary: 服务器错误
                  value:
                    code: 1
                    message: "获取渠道统计列表失败"
                    data: "数据库连接失败"
                    exdata: null
                    token: ""
                    time: 1754292247

tags:
  - name: 统计管理
    description: 统计管理模块相关接口，包括首页统计、趋势统计、渠道统计等功能
