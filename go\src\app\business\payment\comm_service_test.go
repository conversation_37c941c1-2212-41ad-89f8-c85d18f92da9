package payment

import (
	"testing"
)

// TestCalculatePaidAmountDistribution 测试已还金额分配计算
func TestCalculatePaidAmountDistribution(t *testing.T) {
	tests := []struct {
		name                 string
		paidAmount           float64
		totalWaiveAmount     float64
		dueGuaranteeFee      float64
		dueAssetFee          float64
		totalDueAmount       float64
		expectedGuarantee    float64
		expectedAsset        float64
	}{
		{
			name:              "正常分配测试",
			paidAmount:        1000.00,
			totalWaiveAmount:  0.00,
			dueGuaranteeFee:   300.00,
			dueAssetFee:       700.00,
			totalDueAmount:    1000.00,
			expectedGuarantee: 300.00,
			expectedAsset:     700.00,
		},
		{
			name:              "包含减免金额测试",
			paidAmount:        800.00,
			totalWaiveAmount:  200.00,
			dueGuaranteeFee:   300.00,
			dueAssetFee:       700.00,
			totalDueAmount:    1000.00,
			expectedGuarantee: 300.00,
			expectedAsset:     700.00,
		},
		{
			name:              "部分还款测试",
			paidAmount:        500.00,
			totalWaiveAmount:  0.00,
			dueGuaranteeFee:   300.00,
			dueAssetFee:       700.00,
			totalDueAmount:    1000.00,
			expectedGuarantee: 150.00,
			expectedAsset:     350.00,
		},
		{
			name:              "总应还金额为0测试",
			paidAmount:        500.00,
			totalWaiveAmount:  0.00,
			dueGuaranteeFee:   300.00,
			dueAssetFee:       700.00,
			totalDueAmount:    0.00,
			expectedGuarantee: 0.00,
			expectedAsset:     0.00,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			guaranteeAmount, assetAmount := CalculatePaidAmountDistribution(
				tt.paidAmount,
				tt.totalWaiveAmount,
				tt.dueGuaranteeFee,
				tt.dueAssetFee,
				tt.totalDueAmount,
			)

			if guaranteeAmount != tt.expectedGuarantee {
				t.Errorf("担保费分配错误: 期望 %.2f, 实际 %.2f", tt.expectedGuarantee, guaranteeAmount)
			}

			if assetAmount != tt.expectedAsset {
				t.Errorf("资管费分配错误: 期望 %.2f, 实际 %.2f", tt.expectedAsset, assetAmount)
			}
		})
	}
}

// TestConvertToFloat64 测试类型转换函数
func TestConvertToFloat64(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		expected float64
	}{
		{"float64类型", 123.45, 123.45},
		{"float32类型", float32(123.45), float64(float32(123.45))},
		{"int类型", 123, 123.0},
		{"int64类型", int64(123), 123.0},
		{"字符串数字", "123.45", 123.45},
		{"字节数组数字", []byte("123.45"), 123.45},
		{"nil值", nil, 0.0},
		{"无效字符串", "abc", 0.0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToFloat64(tt.input)
			if result != tt.expected {
				t.Errorf("转换错误: 期望 %.2f, 实际 %.2f", tt.expected, result)
			}
		})
	}
}
