# QueryMerchantsBalance 方法完善说明

## 功能概述

完善了 `QueryMerchantsBalance` 方法，新增了累计逾期金额和未来应收金额的计算功能。

## 新增功能

### 1. 累计逾期金额（OverdueAmount）

**适用商户：** 仅资管、担保商户（小贷商户值为 0）

**计算规则：**
- 筛选条件：账单状态为逾期待支付(3)和逾期部分支付(9)，且相关订单状态为放款中(1)
- 计算逻辑：
  - 当期已还金额 = paid_amount + total_waive_amount
  - 当期已还担保金额 = 已还金额 × (当期应还担保金额 / 当期应还总额)
  - 当期担保逾期金额 = 当期应还担保总额 - 当期已还担保金额
  - 担保累计逾期金额 = 所有逾期相关账单的担保逾期金额之和
  - 资管逾期金额计算类似，当期已还资管金额 = 当期已还金额 - 当期已还担保金额

### 2. 未来应收金额（FutureReceivableAmount）

**适用商户：** 仅资管、担保商户（小贷商户值为 0）

**计算规则：**
- 筛选条件：账单状态为非完结状态(0-待支付；3-逾期待支付；7-部分还款；9-逾期部分支付)，且订单状态为放款中(1)
- 计算逻辑：与逾期金额相同，但包含所有未完结的账单

## 技术实现

### 新增文件和方法

1. **工具函数（utils.go）**
   - `CalculatePaidAmountDistribution`: 计算已还金额在担保费和资管费之间的分配

2. **服务方法（comm_service.go）**
   - `calculateMerchantAmounts`: 通用计算方法
   - `calculateMerchantOverdueAmounts`: 计算逾期金额
   - `calculateMerchantFutureReceivableAmounts`: 计算未来应收金额
   - `convertToFloat64`: 类型转换辅助函数

### 性能优化

1. **并发处理**：使用 errgroup 并发执行第三方支付查询和内部计算
2. **数据库优化**：使用 JOIN 查询减少数据库访问次数
3. **精度保证**：使用 decimal 库确保金融计算精度

### 数据库查询

```sql
SELECT 
    brb.due_guarantee_fee,
    brb.asset_management_entry,
    brb.paid_amount,
    brb.total_waive_amount,
    brb.total_due_amount,
    brb.status
FROM business_repayment_bills brb
LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id
WHERE blo.status = 1  -- 放款中
AND brb.status IN (状态列表)
```

## 测试

已添加完整的单元测试：
- `TestCalculatePaidAmountDistribution`: 测试金额分配计算
- `TestConvertToFloat64`: 测试类型转换

## 使用示例

```go
service := NewPaymentService(ctx)
merchants, err := service.QueryMerchantsBalance()
if err != nil {
    // 处理错误
}

// merchants[0] - 资管公司
// merchants[1] - 担保公司  
// merchants[2] - 小贷公司

fmt.Printf("资管公司逾期金额: %s\n", merchants[0].OverdueAmount)
fmt.Printf("担保公司未来应收: %s\n", merchants[1].FutureReceivableAmount)
```

## 注意事项

1. **数据一致性**：确保账单和订单数据的一致性
2. **性能监控**：建议监控查询耗时，特别是在数据量大的情况下
3. **错误处理**：完善的错误处理和超时控制
4. **精度问题**：使用 decimal 库避免浮点数精度问题
