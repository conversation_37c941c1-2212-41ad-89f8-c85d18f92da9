package main

import (
	Service "fincore/app/dianziqian/service"
	"fincore/app/scheduler"
	"fincore/migrate"
	"fincore/utils/config"

	// "fincore/app/scheduler"
	"fincore/bootstrap"
	"fincore/global"
	"runtime"
	"strconv"
)

func main() {
	// 初始化临时日志
	global.App.Log = bootstrap.InitializeLog()
	defer global.App.Log.Sync()

	// 初始化配置
	global.App.Config = config.InitializeConfig()

	// 在配置加载后重新初始化日志
	global.App.Log = bootstrap.ReinitializeLog()

	// 进行数据库迁移
	migrate.Migrate()

	Service.InitService()

	// 初始化定时任务调度器
	if err := scheduler.Initialize(); err != nil {
		global.App.Log.Error("初始化定时任务调度器失败: " + err.Error())
		panic(err)
	}

	// 启动定时任务调度器
	if err := scheduler.Start(); err != nil {
		global.App.Log.Error("启动定时任务调度器失败: " + err.Error())
		panic(err)
	}

	global.App.Log.Info("项目启动成功")
	//加载配置
	cpu_num, _ := strconv.Atoi(global.App.Config.App.CPUnum)
	mycpu := runtime.NumCPU()
	if cpu_num > mycpu { //如果配置cpu核数大于当前计算机核数，则等当前计算机核数
		cpu_num = mycpu
	}
	if cpu_num > 0 {
		runtime.GOMAXPROCS(cpu_num)
	} else {
		runtime.GOMAXPROCS(mycpu)
	}

	// 启动服务器
	bootstrap.RunServer()
}
