package payment

import (
	"context"
	"fincore/global"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/config"
	"fmt"
	"time"

	"golang.org/x/sync/errgroup"
)

type MerchantsQueryAcountBalanceResponse struct {
	MerNo                   string `json:"mer_no"`                    // 商户号
	MerchantName            string `json:"merchant_name"`             // 商户名称（公司名称）
	MerchantType            string `json:"merchant_type"`             // 商户类型
	Platform                string `json:"platform_name"`             // 平台
	TotalBalance            string `json:"total_balance"`             // 总余额
	PendingConversionAmount string `json:"pending_conversion_amount"` // 待转化额度
	WithdrawableAmount      string `json:"withdrawable_amount"`       // 可提现额度
	OverdueAmount           string `json:"overdue_amount"`            // 累计逾期金额
	FutureReceivableAmount  string `json:"future_receivable_amount"`  // 未来应收金额
}

// QueryMerchantsBalance 查询商户余额
func (s *PaymentService) QueryMerchantsBalance() (resp []*MerchantsQueryAcountBalanceResponse, err error) {

	resp = make([]*MerchantsQueryAcountBalanceResponse, 3)

	conf := global.App.Config.SumPay
	resp[0] = &MerchantsQueryAcountBalanceResponse{
		MerNo:        conf.AssetMerchant.MerNo,
		MerchantName: "武汉市几何资产投资管理有限公司",
		MerchantType: "资管公司",
		Platform:     "TTF",
	}

	resp[1] = &MerchantsQueryAcountBalanceResponse{
		MerNo:        conf.GuaranteeMerchant.MerNo,
		MerchantName: "武汉盛唐融资担保有限公司",
		MerchantType: "担保公司",
		Platform:     "TTF",
	}

	resp[2] = &MerchantsQueryAcountBalanceResponse{
		MerNo:        conf.SmallLoanMerchant.MerNo,
		MerchantName: "武汉市几何资产投资管理有限公司",
		MerchantType: "小贷公司",
		Platform:     "TTF",
	}

	mechantConfList := []config.Merchant{
		conf.AssetMerchant,
		conf.GuaranteeMerchant,
		conf.SmallLoanMerchant,
	}

	sumpayService, err := sumpay.NewSumpayService(sumpay.WithContext(s.ctx))
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(s.ctx, 10*time.Second)
	defer cancel()

	g, _ := errgroup.WithContext(ctx)

	for index, merchantConf := range mechantConfList {
		g.Go(func() error {
			conf := merchantConf
			i := index
			merInfo := resp[i]
			sumpayResp, err := sumpayService.MerchantQueryAcountBalance(conf)
			if err != nil {
				return err
			}

			if sumpayResp.RespCode != sumpay.RespCodeSuccess {
				return fmt.Errorf("商户余额查询失败: %s", sumpayResp.RespMsg)
			}

			sumpayRespData := sumpayResp.SumpayMerchantQueryBalanceResponse
			merInfo.TotalBalance = sumpayRespData.TotalBalance
			merInfo.PendingConversionAmount = sumpayRespData.UnsettledBalance
			merInfo.WithdrawableAmount = sumpayRespData.WithdrawBalance
			resp[i] = merInfo
			return nil
		})

	}

	if err = g.Wait(); err != nil {
		return nil, err
	}

	return
}
