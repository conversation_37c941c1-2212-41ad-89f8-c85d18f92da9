package payment

import (
	"context"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/config"
	"fincore/utils/shopspringutils"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/util/gconv"
	"golang.org/x/sync/errgroup"
)

type QueryMerchantsBalanceResponse struct {
	MerchantsBalance        []*MerchantsQueryAcountBalance `json:"merchants_balance"`         // 各商户额度
	TotalBalance            string                         `json:"total_balance"`             // 账户总额度
	PendingConversionAmount string                         `json:"pending_conversion_amount"` // 待转化总额度
	WithdrawableAmount      string                         `json:"withdrawable_amount"`       // 可提现总额度
	OverdueAmount           string                         `json:"overdue_amount"`            // 累计逾期总额度
	FutureReceivableAmount  string                         `json:"future_receivable_amount"`  // 未来应收总额度
}

type MerchantsQueryAcountBalance struct {
	MerNo                   string `json:"mer_no"`                    // 商户号
	MerchantName            string `json:"merchant_name"`             // 商户名称（公司名称）
	MerchantType            string `json:"merchant_type"`             // 商户类型
	Platform                string `json:"platform_name"`             // 平台
	TotalBalance            string `json:"total_balance"`             // 总余额
	PendingConversionAmount string `json:"pending_conversion_amount"` // 待转化额度
	WithdrawableAmount      string `json:"withdrawable_amount"`       // 可提现额度
	OverdueAmount           string `json:"overdue_amount"`            // 累计逾期金额
	FutureReceivableAmount  string `json:"future_receivable_amount"`  // 未来应收金额
}

// QueryMerchantsBalance 查询商户余额
func (s *PaymentService) QueryMerchantsBalance() (resp *QueryMerchantsBalanceResponse, err error) {
	merchants := make([]*MerchantsQueryAcountBalance, 3)

	resp = &QueryMerchantsBalanceResponse{
		MerchantsBalance: merchants,
	}

	conf := global.App.Config.SumPay
	merchants[0] = &MerchantsQueryAcountBalance{
		MerNo:        conf.AssetMerchant.MerNo,
		MerchantName: "武汉市几何资产投资管理有限公司",
		MerchantType: "资管公司",
		Platform:     "TTF",
	}

	merchants[1] = &MerchantsQueryAcountBalance{
		MerNo:        conf.GuaranteeMerchant.MerNo,
		MerchantName: "武汉盛唐融资担保有限公司",
		MerchantType: "担保公司",
		Platform:     "TTF",
	}

	merchants[2] = &MerchantsQueryAcountBalance{
		MerNo:        conf.SmallLoanMerchant.MerNo,
		MerchantName: "武汉市几何资产投资管理有限公司",
		MerchantType: "小贷公司",
		Platform:     "TTF",
	}

	mechantConfList := []config.Merchant{
		conf.AssetMerchant,
		conf.GuaranteeMerchant,
		conf.SmallLoanMerchant,
	}

	sumpayService, err := sumpay.NewSumpayService(sumpay.WithContext(s.ctx))
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithTimeout(s.ctx, 15*time.Second)
	defer cancel()

	g, _ := errgroup.WithContext(ctx)

	// 并发查询第三方支付接口
	for index, merchantConf := range mechantConfList {
		g.Go(func() error {
			conf := merchantConf
			i := index
			merInfo := merchants[i]
			sumpayResp, err := sumpayService.MerchantQueryAcountBalance(conf)
			if err != nil {
				return err
			}

			if sumpayResp.RespCode != sumpay.RespCodeSuccess {
				return fmt.Errorf("商户余额查询失败: %s", sumpayResp.RespMsg)
			}

			sumpayRespData := sumpayResp.SumpayMerchantQueryBalanceResponse
			merInfo.TotalBalance = sumpayRespData.TotalBalance
			merInfo.PendingConversionAmount = sumpayRespData.UnsettledBalance
			merInfo.WithdrawableAmount = sumpayRespData.WithdrawBalance
			merchants[i] = merInfo
			return nil
		})
	}

	// 并发计算累计逾期金额和未来应收金额
	var guaranteeOverdue, assetOverdue, guaranteeFuture, assetFuture float64

	g.Go(func() error {
		guaranteeOverdue, assetOverdue, err = s.calculateMerchantOverdueAmounts()
		return err
	})

	g.Go(func() error {
		guaranteeFuture, assetFuture, err = s.calculateMerchantFutureReceivableAmounts()
		return err
	})

	if err = g.Wait(); err != nil {
		return nil, err
	}

	// 设置累计逾期金额和未来应收金额
	merchants[0].OverdueAmount = fmt.Sprintf("%.2f", assetOverdue)         // 资管公司
	merchants[0].FutureReceivableAmount = fmt.Sprintf("%.2f", assetFuture) // 资管公司

	merchants[1].OverdueAmount = fmt.Sprintf("%.2f", guaranteeOverdue)         // 担保公司
	merchants[1].FutureReceivableAmount = fmt.Sprintf("%.2f", guaranteeFuture) // 担保公司

	merchants[2].OverdueAmount = "0.00"          // 小贷公司
	merchants[2].FutureReceivableAmount = "0.00" // 小贷公司

	totalBalance := shopspringutils.AddAmountsWithDecimal(gconv.Float64(merchants[0].TotalBalance), gconv.Float64(merchants[1].TotalBalance))
	totalBalance = shopspringutils.AddAmountsWithDecimal(totalBalance, gconv.Float64(merchants[2].TotalBalance))

	pendingConversionAmount := shopspringutils.AddAmountsWithDecimal(gconv.Float64(merchants[0].PendingConversionAmount), gconv.Float64(merchants[1].PendingConversionAmount))
	pendingConversionAmount = shopspringutils.AddAmountsWithDecimal(pendingConversionAmount, gconv.Float64(merchants[2].PendingConversionAmount))

	withdrawableAmount := shopspringutils.AddAmountsWithDecimal(gconv.Float64(merchants[0].WithdrawableAmount), gconv.Float64(merchants[1].WithdrawableAmount))
	withdrawableAmount = shopspringutils.AddAmountsWithDecimal(withdrawableAmount, gconv.Float64(merchants[2].WithdrawableAmount))

	overdueAmount := shopspringutils.AddAmountsWithDecimal(gconv.Float64(merchants[0].OverdueAmount), gconv.Float64(merchants[1].OverdueAmount))
	overdueAmount = shopspringutils.AddAmountsWithDecimal(overdueAmount, gconv.Float64(merchants[2].OverdueAmount))

	futureReceivableAmount := shopspringutils.AddAmountsWithDecimal(gconv.Float64(merchants[0].FutureReceivableAmount), gconv.Float64(merchants[1].FutureReceivableAmount))
	futureReceivableAmount = shopspringutils.AddAmountsWithDecimal(futureReceivableAmount, gconv.Float64(merchants[2].FutureReceivableAmount))

	resp.TotalBalance = fmt.Sprintf("%.2f", totalBalance)
	resp.PendingConversionAmount = fmt.Sprintf("%.2f", pendingConversionAmount)
	resp.WithdrawableAmount = fmt.Sprintf("%.2f", withdrawableAmount)
	resp.OverdueAmount = fmt.Sprintf("%.2f", overdueAmount)
	resp.FutureReceivableAmount = fmt.Sprintf("%.2f", futureReceivableAmount)

	return
}

// calculateMerchantAmounts 根据账单状态计算商户金额
func (s *PaymentService) calculateMerchantAmounts(billStatuses []int) (guaranteeAmount, assetAmount float64, err error) {
	if len(billStatuses) == 0 {
		return 0, 0, nil
	}

	// 使用 repository 查询数据
	repository := NewPaymentRepository(s.ctx)
	data, err := repository.GetBillAmountsByStatus(s.ctx, billStatuses)
	if err != nil {
		return 0, 0, err
	}

	var totalGuaranteeAmount, totalAssetAmount float64

	for _, item := range data {
		// 使用 gconv 进行类型转换
		dueGuaranteeFee := gconv.Float64(item["due_guarantee_fee"])
		assetManagementEntry := gconv.Float64(item["asset_management_entry"])
		paidAmount := gconv.Float64(item["paid_amount"])
		totalWaiveAmount := gconv.Float64(item["total_waive_amount"])
		totalDueAmount := gconv.Float64(item["total_due_amount"])

		// 计算已还金额在担保费和资管费之间的分配
		paidGuaranteeAmount, paidAssetAmount := CalculatePaidAmountDistribution(
			paidAmount,
			totalWaiveAmount,
			dueGuaranteeFee,
			assetManagementEntry,
			totalDueAmount,
		)

		// 计算未还的担保费和资管费
		remainingGuaranteeAmount := shopspringutils.SubtractAmountsWithDecimal(dueGuaranteeFee, paidGuaranteeAmount)
		remainingAssetAmount := shopspringutils.SubtractAmountsWithDecimal(assetManagementEntry, paidAssetAmount)

		// 确保金额不为负数
		if remainingGuaranteeAmount < 0 {
			remainingGuaranteeAmount = 0
		}
		if remainingAssetAmount < 0 {
			remainingAssetAmount = 0
		}

		totalGuaranteeAmount = shopspringutils.AddAmountsWithDecimal(totalGuaranteeAmount, remainingGuaranteeAmount)
		totalAssetAmount = shopspringutils.AddAmountsWithDecimal(totalAssetAmount, remainingAssetAmount)
	}

	return totalGuaranteeAmount, totalAssetAmount, nil
}

// calculateMerchantOverdueAmounts 计算商户累计逾期金额
func (s *PaymentService) calculateMerchantOverdueAmounts() (guaranteeOverdue, assetOverdue float64, err error) {
	// 逾期状态：3-逾期待支付，9-逾期部分支付
	overdueStatuses := []int{
		model.RepaymentBillStatusOverdueUnpaid,      // 3-逾期待支付
		model.RepaymentBillStatusOverduePartialPaid, // 9-逾期部分支付
	}

	return s.calculateMerchantAmounts(overdueStatuses)
}

// calculateMerchantFutureReceivableAmounts 计算商户未来应收金额
func (s *PaymentService) calculateMerchantFutureReceivableAmounts() (guaranteeFuture, assetFuture float64, err error) {
	// 未完结状态：0-待支付，3-逾期待支付，7-部分还款，9-逾期部分支付
	pendingStatuses := []int{
		model.RepaymentBillStatusUnpaid,             // 0-待支付
		model.RepaymentBillStatusOverdueUnpaid,      // 3-逾期待支付
		model.RepaymentBillStatusPartialPaid,        // 7-部分还款
		model.RepaymentBillStatusOverduePartialPaid, // 9-逾期部分支付
	}

	return s.calculateMerchantAmounts(pendingStatuses)
}
